'use client';

import { useEffect, useRef } from 'react';
import { useInView, useAnimation } from 'framer-motion';

interface UseScrollRevealOptions {
  triggerOnce?: boolean;
  delay?: number;
}

export function useScrollReveal(options: UseScrollRevealOptions = {}) {
  const { triggerOnce = true, delay = 0 } = options;
  const ref = useRef(null);
  const isInView = useInView(ref, {
    once: triggerOnce
  });
  const controls = useAnimation();

  useEffect(() => {
    if (isInView) {
      controls.start({
        opacity: 1,
        y: 0,
        transition: {
          duration: 0.4,
          ease: [0.4, 0, 0.2, 1],
          delay,
        },
      });
    } else if (!triggerOnce) {
      controls.start({
        opacity: 0,
        y: 20,
        transition: {
          duration: 0.4,
          ease: [0.4, 0, 0.2, 1],
        },
      });
    }
  }, [isInView, controls, triggerOnce, delay]);

  return {
    ref,
    controls,
    initial: { opacity: 0, y: 20 },
    animate: controls,
  };
}
