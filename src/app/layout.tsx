import type { Metadata, Viewport } from "next";
import Script from "next/script";
import "./globals.css";

export const metadata: Metadata = {
  title: "Celer AI Studio - Intelligent Design & Growth",
  description: "We are a design and growth studio for passionate founders. We build the strategic brand and digital presence you need to unlock your true business potential.",
  keywords: ["design studio", "branding", "web development", "growth", "founders", "startup", "digital agency", "UI/UX design"],
  authors: [{ name: "Celer AI Studio" }],
  creator: "Celer AI Studio",
  publisher: "Celer AI Studio",
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  openGraph: {
    title: "Celer AI Studio - Intelligent Design & Growth",
    description: "We are a design and growth studio for passionate founders. We build the strategic brand and digital presence you need to unlock your true business potential.",
    url: "https://celerai.live",
    siteName: "Celer AI Studio",
    type: "website",
    locale: "en_US",
  },
  twitter: {
    card: "summary_large_image",
    title: "Celer AI Studio - Intelligent Design & Growth",
    description: "We are a design and growth studio for passionate founders. We build the strategic brand and digital presence you need to unlock your true business potential.",
    creator: "@celeraistudio",
  },
};

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="dark">
      <head>
        {/* Google Analytics 4 */}
        <Script
          src="https://www.googletagmanager.com/gtag/js?id=G-XXXXXXXXXX"
          strategy="afterInteractive"
        />
        <Script id="google-analytics" strategy="afterInteractive">
          {`
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', 'G-XXXXXXXXXX');
          `}
        </Script>
      </head>
      <body className="antialiased">
        {children}
      </body>
    </html>
  );
}
