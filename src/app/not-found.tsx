import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { TextReveal } from '@/components/ui/text-reveal';

export default function NotFound() {
  return (
    <div className="min-h-screen bg-background flex items-center justify-center px-6">
      <div className="max-w-2xl text-center space-y-8">
        <div className="space-y-4">
          <TextReveal
            className="text-hero text-foreground"
            split="word"
            from="bottom"
            delay={0.05}
          >
            404
          </TextReveal>
          <TextReveal
            className="text-section-title text-foreground"
            split="word"
            from="bottom"
            delay={0.1}
          >
            Page Not Found
          </TextReveal>
          <p className="text-body text-muted-foreground max-w-lg mx-auto">
            The page you&apos;re looking for doesn&apos;t exist. It might have been moved, deleted, or you entered the wrong URL.
          </p>
        </div>
        
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button
            asChild
            className="text-button transition-custom transition-hover hover:button-hover-shadow"
          >
            <Link href="/">
              Back to Home
            </Link>
          </Button>
          <Button
            variant="outline"
            asChild
            className="text-button"
          >
            <a href="mailto:<EMAIL>">
              Contact Us
            </a>
          </Button>
        </div>
      </div>
    </div>
  );
}
