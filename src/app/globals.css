@import url('https://api.fontshare.com/v2/css?f[]=satoshi@400,500,700&display=swap');
@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  /* Celerai Studio Design System */
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-border: var(--border);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);

  /* Typography */
  --font-sans: 'Satoshi', system-ui, sans-serif;
  --font-satoshi: 'Satoshi', system-ui, sans-serif;

  /* Radius */
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  /* Animation */
  --transition-timing: cubic-bezier(0.4, 0, 0.2, 1);
}

:root {
  --radius: 0.875rem;

  /* Premium Agency Color Palette - Light Mode (Steve Jobs inspired) */
  --background: #FDFDFD; /* Ultra-clean background with subtle warmth */
  --foreground: #0F0F0F; /* Deep, sophisticated text */
  --card: #FFFFFF; /* Pure white cards with subtle elevation */
  --card-foreground: #0F0F0F; /* Card text */
  --popover: #FFFFFF;
  --popover-foreground: #0F0F0F;
  --primary: #0F0F0F; /* Primary actions in sophisticated black */
  --primary-foreground: #FFFFFF;
  --secondary: #F8F9FA; /* Subtle secondary background */
  --secondary-foreground: #0F0F0F;
  --muted: #F5F6F7; /* Very subtle muted background */
  --muted-foreground: #64748B; /* Refined muted text */
  --accent: #2563EB; /* Premium blue for key interactions */
  --accent-foreground: #FFFFFF;
  --destructive: #DC2626;
  --border: #E2E8F0; /* Subtle, refined borders */
  --input: #F8FAFC;
  --ring: #2563EB;

  /* Enhanced Luxury Shadow System */
  --shadow-luxury-sm: 0 1px 2px rgba(0, 0, 0, 0.04), 0 1px 3px rgba(0, 0, 0, 0.06);
  --shadow-luxury-md: 0 4px 6px rgba(0, 0, 0, 0.04), 0 2px 4px rgba(0, 0, 0, 0.06);
  --shadow-luxury-lg: 0 10px 15px rgba(0, 0, 0, 0.06), 0 4px 6px rgba(0, 0, 0, 0.04);
  --shadow-luxury-xl: 0 20px 25px rgba(0, 0, 0, 0.08), 0 10px 10px rgba(0, 0, 0, 0.04);
  --shadow-luxury-2xl: 0 25px 50px rgba(0, 0, 0, 0.10), 0 12px 24px rgba(0, 0, 0, 0.06);

  /* Premium Gradient System */
  --gradient-primary: linear-gradient(135deg, #2563EB 0%, #1D4ED8 100%);
  --gradient-secondary: linear-gradient(135deg, #F8FAFC 0%, #F1F5F9 100%);
  --gradient-accent: linear-gradient(135deg, #3B82F6 0%, #2563EB 100%);
}

.dark {
  /* Premium Dark Luxury Color Palette - Consistent Dark Hues */
  --background: #0A0A0A; /* Deep black for maximum contrast */
  --foreground: #F5F5F5; /* Clean white for text */
  --card: #111111; /* Premium dark card background */
  --card-foreground: #F5F5F5;
  --popover: #111111;
  --popover-foreground: #F5F5F5;
  --primary: #007CF0; /* Premium blue - consistent throughout */
  --primary-foreground: #FFFFFF;
  --secondary: #1A1A1A; /* Sophisticated dark gray */
  --secondary-foreground: #F5F5F5;
  --muted: #151515; /* Subtle muted background */
  --muted-foreground: #888888; /* Refined muted text */
  --accent: #007CF0; /* Same as primary for consistency */
  --accent-foreground: #FFFFFF;
  --destructive: #DC2626;
  --border: #262626; /* Subtle borders */
  --input: #111111;
  --ring: #007CF0;

  /* Enhanced Shadow System - Premium Dark */
  --shadow-luxury-sm: 0 2px 8px rgba(0, 0, 0, 0.4), 0 1px 3px rgba(0, 0, 0, 0.5);
  --shadow-luxury-md: 0 4px 12px rgba(0, 0, 0, 0.5), 0 2px 6px rgba(0, 0, 0, 0.6);
  --shadow-luxury-lg: 0 8px 25px rgba(0, 0, 0, 0.6), 0 4px 10px rgba(0, 0, 0, 0.7);
  --shadow-luxury-xl: 0 12px 35px rgba(0, 0, 0, 0.7), 0 6px 15px rgba(0, 0, 0, 0.8);
  --shadow-luxury-2xl: 0 20px 50px rgba(0, 0, 0, 0.8), 0 10px 25px rgba(0, 0, 0, 0.9);

  /* Premium button hover shadow */
  --shadow-button-hover: 0px 8px 25px rgba(0, 124, 240, 0.3);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }

  html {
    scroll-behavior: smooth;
  }

  body {
    @apply bg-background text-foreground;
    font-family: var(--font-satoshi);
    font-feature-settings: 'rlig' 1, 'calt' 1;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  .font-satoshi {
    font-family: var(--font-satoshi);
  }
}

@layer components {
  /* Premium Typography Scale - No gradients, pure luxury */
  .text-hero {
    font-size: 48px;
    font-weight: 700;
    letter-spacing: -0.025em;
    line-height: 1.1;
    font-feature-settings: 'kern' 1, 'liga' 1, 'ss01' 1;
    color: var(--foreground);
  }

  .text-section-title {
    font-size: 36px;
    font-weight: 600;
    letter-spacing: -0.02em;
    line-height: 1.2;
    font-feature-settings: 'kern' 1, 'liga' 1, 'ss01' 1;
  }

  /* Desktop Typography - Enhanced for luxury feel */
  @media (min-width: 768px) {
    .text-hero {
      font-size: 72px;
      letter-spacing: -0.03em;
      line-height: 1.05;
    }

    .text-section-title {
      font-size: 48px;
      letter-spacing: -0.025em;
    }
  }

  @media (min-width: 1024px) {
    .text-hero {
      font-size: 84px;
      letter-spacing: -0.035em;
    }

    .text-section-title {
      font-size: 56px;
      letter-spacing: -0.03em;
    }
  }

  .text-card-title {
    font-size: 24px;
    font-weight: 600;
    line-height: 1.3;
    letter-spacing: -0.01em;
  }

  .text-body {
    font-size: 18px;
    font-weight: 400;
    line-height: 1.7;
    color: var(--muted-foreground);
  }

  .text-body-large {
    font-size: 20px;
    font-weight: 400;
    line-height: 1.6;
    color: var(--muted-foreground);
  }

  .text-button {
    font-size: 16px;
    font-weight: 500;
    letter-spacing: 0.01em;
  }

  /* Luxury Layout & Spacing */
  .section-spacing {
    padding-top: 120px;
    padding-bottom: 120px;
  }

  .section-spacing-lg {
    padding-top: 160px;
    padding-bottom: 160px;
  }

  .section-spacing-xl {
    padding-top: 200px;
    padding-bottom: 200px;
  }

  .max-content-width {
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
  }

  /* Luxury Shadow System */
  .shadow-luxury-sm {
    box-shadow: var(--shadow-luxury-sm);
  }

  .shadow-luxury-md {
    box-shadow: var(--shadow-luxury-md);
  }

  .shadow-luxury-lg {
    box-shadow: var(--shadow-luxury-lg);
  }

  .shadow-luxury-xl {
    box-shadow: var(--shadow-luxury-xl);
  }

  .shadow-luxury-2xl {
    box-shadow: var(--shadow-luxury-2xl);
  }

  /* Legacy shadow classes for compatibility */
  .card-shadow {
    box-shadow: var(--shadow-luxury-md);
  }

  .button-hover-shadow:hover {
    box-shadow: var(--shadow-button-hover);
    transform: translateY(-1px);
  }

  /* 3D Flip Card Effects */
  .perspective-1000 {
    perspective: 1000px;
  }

  .transform-style-preserve-3d {
    transform-style: preserve-3d;
  }

  .backface-hidden {
    backface-visibility: hidden;
  }

  .rotate-y-180 {
    transform: rotateY(180deg);
  }

  /* Enhanced geometric shapes */
  .geometric-accent {
    position: relative;
  }

  .geometric-accent::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, var(--primary), var(--accent));
    border-radius: inherit;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .geometric-accent:hover::before {
    opacity: 0.3;
  }

  /* Luxury Animation System */
  .transition-luxury {
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 300ms;
  }

  .transition-luxury-slow {
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 500ms;
  }

  .transition-hover {
    transition-duration: 300ms;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  }

  .transition-scroll {
    transition-duration: 400ms;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Enhanced scroll reveal animation */
  .scroll-reveal {
    opacity: 0;
    transform: translateY(30px);
    transition: opacity 600ms cubic-bezier(0.4, 0, 0.2, 1),
                transform 600ms cubic-bezier(0.4, 0, 0.2, 1);
  }

  .scroll-reveal.in-view {
    opacity: 1;
    transform: translateY(0);
  }

  /* Luxury interaction states */
  .hover-lift:hover {
    transform: translateY(-2px);
    transition: transform 300ms cubic-bezier(0.4, 0, 0.2, 1);
  }

  .hover-scale:hover {
    transform: scale(1.02);
    transition: transform 300ms cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Premium glass effect */
  .glass-effect {
    backdrop-filter: blur(24px) saturate(180%);
    background: rgba(255, 255, 255, 0.85);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: var(--shadow-luxury-md);
  }

  .dark .glass-effect {
    background: rgba(17, 17, 17, 0.85);
    border: 1px solid rgba(255, 255, 255, 0.15);
  }

  /* Premium gradient backgrounds */
  .gradient-luxury {
    background: var(--gradient-secondary);
  }

  .dark .gradient-luxury {
    background: linear-gradient(135deg, #0F172A 0%, #1E293B 100%);
  }

  /* Enhanced interaction states */
  .hover-lift-subtle:hover {
    transform: translateY(-1px);
    transition: transform 200ms cubic-bezier(0.4, 0, 0.2, 1);
  }

  .hover-glow:hover {
    box-shadow: var(--shadow-luxury-xl), 0 0 0 1px var(--accent);
    transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Premium button styles */
  .btn-premium {
    background: var(--gradient-primary);
    border: 1px solid transparent;
    box-shadow: var(--shadow-luxury-md);
    transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
  }

  .btn-premium:hover {
    box-shadow: var(--shadow-luxury-xl);
    transform: translateY(-1px);
  }

  /* Enhanced focus states */
  .focus-ring:focus-visible {
    outline: 2px solid var(--accent);
    outline-offset: 2px;
    border-radius: var(--radius);
  }

  /* Smooth scrolling enhancement */
  @media (prefers-reduced-motion: no-preference) {
    html {
      scroll-behavior: smooth;
    }
  }

  /* Premium selection styles */
  ::selection {
    background: var(--accent);
    color: var(--accent-foreground);
  }

  /* Enhanced scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: var(--muted);
  }

  ::-webkit-scrollbar-thumb {
    background: var(--muted-foreground);
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: var(--accent);
  }

  /* Sliding logos animation */
  @keyframes scroll {
    0% {
      transform: translateX(0);
    }
    100% {
      transform: translateX(-50%);
    }
  }

  .animate-scroll {
    animation: scroll 30s linear infinite;
  }
}
