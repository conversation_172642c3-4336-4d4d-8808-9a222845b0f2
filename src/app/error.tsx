'use client';

import { useEffect } from 'react';
import { Button } from '@/components/ui/button';

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error(error);
  }, [error]);

  return (
    <div className="min-h-screen bg-background flex items-center justify-center px-6">
      <div className="max-w-md text-center space-y-6">
        <div className="space-y-2">
          <h1 className="text-section-title text-foreground">
            Something went wrong
          </h1>
          <p className="text-body text-muted-foreground">
            We apologize for the inconvenience. Please try refreshing the page or contact us if the problem persists.
          </p>
        </div>
        
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button
            onClick={reset}
            className="text-button transition-custom transition-hover hover:button-hover-shadow"
          >
            Try again
          </Button>
          <Button
            variant="outline"
            asChild
            className="text-button"
          >
            <a href="mailto:<EMAIL>">
              Contact Support
            </a>
          </Button>
        </div>
      </div>
    </div>
  );
}
