'use client';
import {
  motion,
  MotionValue,
  Transition,
  useAnimation,
  useMotionValue,
} from 'framer-motion';
import { useEffect, useState } from 'react';

type CircularTextProps = {
  text: string;
  spinDuration?: number;
  onHover?: 'slowDown' | 'speedUp' | 'pause' | 'goBonkers';
  className?: string;
  size?: 'small' | 'medium' | 'large';
};

const getRotationTransition = (
  duration: number,
  from: number,
  loop: boolean = true,
) => ({
  from,
  to: from + 360,
  ease: 'linear' as const,
  duration,
  type: 'tween' as const,
  repeat: loop ? Infinity : 0,
});

const getTransition = (duration: number, from: number) => ({
  rotate: getRotationTransition(duration, from),
  scale: {
    type: 'spring' as const,
    damping: 20,
    stiffness: 300,
  },
});

export function CircularText({
  text = 'Circular Text Animation • ',
  spinDuration = 20,
  onHover = 'speedUp',
  className = '',
  size = 'large',
}: Readonly<CircularTextProps>) {
  const [mounted, setMounted] = useState(false);
  const letters = Array.from(text);
  const controls = useAnimation();
  const rotation: MotionValue<number> = useMotionValue(0);

  useEffect(() => {
    setMounted(true);

    // Start the continuous rotation animation
    controls.start({
      rotate: 360,
      transition: {
        duration: spinDuration,
        ease: "linear",
        repeat: Infinity,
      },
    });
  }, [controls, spinDuration]);

  useEffect(() => {
    const start = rotation.get();
    controls.start({
      rotate: start + 360,
      scale: 1,
      transition: getTransition(spinDuration, start),
    });
  }, [spinDuration, text, onHover, controls, rotation]);

  const handleHoverStart = () => {
    const start = rotation.get();

    if (!onHover) return;

    let transitionConfig: ReturnType<typeof getTransition> | Transition;
    let scaleVal = 1;

    switch (onHover) {
      case 'slowDown':
        transitionConfig = getTransition(spinDuration * 2, start);
        break;
      case 'speedUp':
        transitionConfig = getTransition(spinDuration / 4, start);
        break;
      case 'pause':
        transitionConfig = {
          rotate: { type: 'spring', damping: 20, stiffness: 300 },
          scale: { type: 'spring', damping: 20, stiffness: 300 },
        };
        break;
      case 'goBonkers':
        transitionConfig = getTransition(spinDuration / 20, start);
        scaleVal = 0.8;
        break;
      default:
        transitionConfig = getTransition(spinDuration, start);
    }

    controls.start({
      rotate: start + 360,
      scale: scaleVal,
      transition: transitionConfig,
    });
  };

  const handleHoverEnd = () => {
    const start = rotation.get();
    controls.start({
      rotate: start + 360,
      scale: 1,
      transition: getTransition(spinDuration, start),
    });
  };

  // Size configurations
  const sizeConfig = {
    small: {
      containerHeight: 'min-h-[120px]',
      circleSize: 'h-[120px] w-[120px]',
      textSize: 'text-sm',
      radius: 45
    },
    medium: {
      containerHeight: 'min-h-[150px]',
      circleSize: 'h-[150px] w-[150px]',
      textSize: 'text-lg',
      radius: 60
    },
    large: {
      containerHeight: 'min-h-[400px]',
      circleSize: 'h-[200px] w-[200px]',
      textSize: 'text-2xl',
      radius: 80
    }
  };

  const config = sizeConfig[size];

  if (!mounted) {
    return (
      <div className={`flex ${config.containerHeight} items-center justify-center rounded-lg`}>
        <div className={`relative m-0 mx-auto ${config.circleSize} origin-center rounded-full ${className}`} />
      </div>
    );
  }

  return (
    <div className={`flex ${config.containerHeight} items-center justify-center rounded-lg`}>
      <motion.div
        className={`relative m-0 mx-auto ${config.circleSize} origin-center cursor-pointer rounded-full text-center font-black text-white ${className}`}
        style={{ rotate: rotation }}
        initial={{ rotate: 0 }}
        animate={controls}
        onMouseEnter={handleHoverStart}
        onMouseLeave={handleHoverEnd}
      >
        {letters.map((letter, i) => {
          const rotationDeg = (360 / letters.length) * i;
          const radius = config.radius;
          const x = Math.cos((rotationDeg * Math.PI) / 180) * radius;
          const y = Math.sin((rotationDeg * Math.PI) / 180) * radius;

          return (
            <span
              key={`${text}-${i}`}
              className={`absolute ${config.textSize} transition-all duration-500 ease-out`}
              style={{
                left: '50%',
                top: '50%',
                transform: `translate(-50%, -50%) translate(${x}px, ${y}px) rotate(${rotationDeg + 90}deg)`,
                transformOrigin: 'center center'
              }}
            >
              {letter}
            </span>
          );
        })}
      </motion.div>
    </div>
  );
}

export default CircularText;
