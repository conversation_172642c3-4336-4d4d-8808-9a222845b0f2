'use client';

import { ElementRef, useRef } from 'react';
import { useInView } from 'framer-motion';

export const Container = ({
  children,
  className,
  id,
}: {
  children: React.ReactNode;
  className?: string;
  id?: string;
}) => {
  const ref = useRef<ElementRef<'div'>>(null);
  // Animate when the top of the element is 150px into view
  const isInView = useInView(ref, { once: true, margin: '-150px 0px' });

  return (
    <section id={id} className={`section-spacing ${className || ''}`}>
      <div
        ref={ref}
        className="max-content-width px-6"
        style={{
          transform: isInView ? 'none' : 'translateY(30px)',
          opacity: isInView ? 1 : 0,
          transition: 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)',
        }}
      >
        {children}
      </div>
    </section>
  );
};
