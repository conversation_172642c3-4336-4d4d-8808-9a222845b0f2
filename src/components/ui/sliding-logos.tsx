'use client';

import React from 'react';
import { OpenAI, Aws, Azure, Cloudflare, Google, Vercel } from '@lobehub/icons';

const logos = [
  { name: 'OpenAI', component: OpenAI },
  { name: 'AWS', component: Aws.Color },
  { name: 'Azure', component: Azure.Color },
  { name: '<PERSON>flare', component: Cloudflare.Color },
  { name: 'Google', component: Google.Color },
  { name: 'Vercel', component: Vercel.Text },
];

export function SlidingLogos() {
  return (
    <div className="w-full overflow-hidden py-6">
      <div className="flex animate-scroll space-x-16">
        {/* First set of logos */}
        {logos.map((logo, index) => {
          const LogoComponent = logo.component;
          return (
            <div
              key={`first-${index}`}
              className="flex-shrink-0 flex items-center justify-center opacity-40 hover:opacity-80 transition-opacity duration-300"
            >
              <LogoComponent size={32} />
            </div>
          );
        })}
        {/* Duplicate set for seamless loop */}
        {logos.map((logo, index) => {
          const LogoComponent = logo.component;
          return (
            <div
              key={`second-${index}`}
              className="flex-shrink-0 flex items-center justify-center opacity-40 hover:opacity-80 transition-opacity duration-300"
            >
              <LogoComponent size={32} />
            </div>
          );
        })}
      </div>
    </div>
  );
}
