import React from 'react';
import { OpenAI, Aws, Azure, Cloudflare, Google, Vercel } from '@lobehub/icons';

// Wrapper components for consistent sizing and styling
export const OpenAILogo = ({ className }: { className?: string }) => (
  <div className={className}>
    <OpenAI size={56} />
  </div>
);

export const AWSLogo = ({ className }: { className?: string }) => (
  <div className={className}>
    <Aws.Color size={56} />
  </div>
);

export const AzureLogo = ({ className }: { className?: string }) => (
  <div className={className}>
    <Azure.Color size={56} />
  </div>
);

export const CloudflareLogo = ({ className }: { className?: string }) => (
  <div className={className}>
    <Cloudflare.Color size={56} />
  </div>
);

export const GoogleLogo = ({ className }: { className?: string }) => (
  <div className={className}>
    <Google.Color size={56} />
  </div>
);

export const VercelLogo = ({ className }: { className?: string }) => (
  <div className={className}>
    <Vercel.Text size={56} />
  </div>
);

export const CompanyLogos = {
  OPENAI: OpenAILogo,
  AWS: AWSLogo,
  AZURE: AzureLogo,
  CLOUDFLARE: CloudflareLogo,
  GOOGLE: GoogleLogo,
  VERCEL: VercelLogo,
};