'use client';

import { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { CircularText } from '@/components/mvpblocks/circular-text';

interface CaseStudyCardProps {
  title: string;
  description: string;
  thumbnailImage: string;
  videoSrc: string;
  showMakersMark?: boolean;
  modalContent: {
    title: string;
    description: string;
    details: string;
  };
}

export function CaseStudyCard({ 
  title, 
  description, 
  thumbnailImage, 
  videoSrc, 
  showMakersMark = false,
  modalContent 
}: CaseStudyCardProps) {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <Card
      className="relative overflow-hidden bg-card border-border card-shadow transition-custom transition-hover group cursor-pointer aspect-[4/3] min-h-[300px]"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Static Thumbnail */}
      <div 
        className={`absolute inset-0 transition-opacity duration-500 ${isHovered ? 'opacity-0' : 'opacity-100'}`}
        style={{
          backgroundImage: `url(${thumbnailImage})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
        }}
      />
      
      {/* Video on Hover */}
      <div 
        className={`absolute inset-0 transition-opacity duration-500 ${isHovered ? 'opacity-100' : 'opacity-0'}`}
      >
        <video
          src={videoSrc}
          autoPlay
          loop
          muted
          className="w-full h-full object-cover"
        />
      </div>
      
      {/* Maker's Mark */}
      {showMakersMark && (
        <div className="absolute bottom-4 right-4 z-10 w-20 h-20">
          <CircularText
            text="• CELERAI CONCEPT •"
            spinDuration={30}
            onHover="pause"
            className="text-xs text-accent"
          />
        </div>
      )}
      
      {/* Content Overlay */}
      <div className="absolute inset-0 bg-gradient-to-t from-background/80 via-transparent to-transparent flex flex-col justify-end p-6 z-10">
        <h3 className="text-card-title text-foreground mb-2">{title}</h3>
        <p className="text-sm text-muted-foreground mb-4">{description}</p>
        
        <Dialog>
          <DialogTrigger asChild>
            <Button variant="outline" size="sm" className="self-start">
              View Concept
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle className="text-section-title">{modalContent.title}</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <p className="text-body text-muted-foreground">{modalContent.description}</p>
              <p className="text-body">{modalContent.details}</p>
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </Card>
  );
}
