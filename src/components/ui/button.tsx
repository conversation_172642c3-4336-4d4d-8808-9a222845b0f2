import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-lg text-sm font-medium transition-luxury disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive hover-lift",
  {
    variants: {
      variant: {
        default:
          "bg-primary text-primary-foreground shadow-luxury-md hover:shadow-luxury-lg hover:bg-primary/90 active:scale-[0.98]",
        destructive:
          "bg-destructive text-white shadow-luxury-md hover:shadow-luxury-lg hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 active:scale-[0.98]",
        outline:
          "border border-border bg-background shadow-luxury-sm hover:bg-secondary hover:shadow-luxury-md hover:text-secondary-foreground active:scale-[0.98]",
        secondary:
          "bg-secondary text-secondary-foreground shadow-luxury-sm hover:bg-secondary/80 hover:shadow-luxury-md active:scale-[0.98]",
        ghost:
          "hover:bg-secondary hover:text-secondary-foreground transition-luxury",
        link: "text-accent underline-offset-4 hover:underline hover:text-accent/80 transition-luxury",
        luxury:
          "bg-gradient-to-r from-primary to-primary/90 text-primary-foreground shadow-luxury-lg hover:shadow-luxury-xl hover:from-primary/90 hover:to-primary/80 active:scale-[0.98] font-medium",
      },
      size: {
        default: "h-10 px-6 py-2 has-[>svg]:px-5 text-base",
        sm: "h-8 rounded-md gap-1.5 px-4 has-[>svg]:px-3 text-sm",
        lg: "h-12 rounded-lg px-8 has-[>svg]:px-6 text-lg font-medium",
        xl: "h-14 rounded-xl px-10 has-[>svg]:px-8 text-xl font-medium",
        icon: "size-10",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

function Button({
  className,
  variant,
  size,
  asChild = false,
  ...props
}: React.ComponentProps<"button"> &
  VariantProps<typeof buttonVariants> & {
    asChild?: boolean
  }) {
  const Comp = asChild ? Slot : "button"

  return (
    <Comp
      data-slot="button"
      className={cn(buttonVariants({ variant, size, className }))}
      {...props}
    />
  )
}

export { Button, buttonVariants }
