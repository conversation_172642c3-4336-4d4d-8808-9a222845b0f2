'use client';

import { motion } from 'framer-motion';
import { useScrollReveal } from '@/hooks/useScrollReveal';
import { cn } from '@/lib/utils';

interface ScrollRevealProps {
  children: React.ReactNode;
  className?: string;
  delay?: number;
  triggerOnce?: boolean;
}

export function ScrollReveal({
  children,
  className,
  delay = 0,
  triggerOnce = true,
}: ScrollRevealProps) {
  const { ref, initial, animate } = useScrollReveal({
    delay,
    triggerOnce,
  });

  return (
    <motion.div
      ref={ref}
      initial={initial}
      animate={animate}
      className={cn(className)}
    >
      {children}
    </motion.div>
  );
}
