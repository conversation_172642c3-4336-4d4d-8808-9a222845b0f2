Project Blueprint: The Celerai Studio Landing Page
To the Developer:

This document contains the complete design system and architectural plan for the Celerai Studio website. Our guiding principle is Intentional Minimalism. Every element, animation, and spacing value has been chosen with purpose. The goal is a premium, confident, and seemingly effortless digital experience.

Your primary role is to execute this specification with precision. Please adhere strictly to the values provided.

Part 1: The Brand Identity
Official Brand Name: Celerai Studio
Website Address: celerai.live
Tagline: Intelligent Design & Growth
Core Vibe: Intentional Minimalism, Calm Confidence, Premium Craftsmanship.
Part 2: The Foundational Design System (Absolute Rules)
This is the DNA of the site. These rules are global and non-negotiable.

1. Color Palette (Material-Based)

Primary Background (#111111): A deep charcoal. Our primary "material."
Card/Layer Background (#1A1A1A): A slightly lighter charcoal to create subtle, physical layers.
Primary Text (#EAEAEA): A soft, high-readability white.
Muted Text (#888888): For less important copy or default-state icons.
Accent Color (#007CF0): Electric Blue. Usage is restricted: Only for primary call-to-action buttons, key interactive links, and specific icon highlights. It is a tool to guide the user, not a decorative color.
Borders (#333333): For any necessary subtle dividers.
2. Typography (The Voice)

Font Family: Satoshi (from Fontshare). Ensure font weights 400, 500, and 700 are loaded.
Scale & Spacing:
H1 (Hero Headlines): 72px, 700 weight, -2px letter-spacing.
H2 (Section Titles): 52px, 500 weight, -1.5px letter-spacing.
H3 (Card Titles): 22px, 500 weight.
Body: 18px, 400 weight, 1.7 line-height.
Buttons/Links: 16px, 500 weight.
3. Layout & Spacing (The Breath)

Max Content Width: 1200px.
Vertical Section Spacing: A minimum of 160px padding-top and padding-bottom for all major sections. This is the most important rule for the premium feel.
Padding Scale: All internal container padding must use an 8px scale (e.g., 16px, 24px, 32px, 48px).
4. Interaction Physics & Animation

Transition Timing: All transitions use cubic-bezier(0.4, 0, 0.2, 1).
Duration: 300ms for hover states, 400ms for on-scroll animations.
On-Scroll Reveal: Elements do not "pop" in. They animate on enter with opacity: 0 to 1 and transform: translateY(20px) to 0.
5. Shadows (The Depth)

Shadows must be subtle and realistic, simulating a single, diffused light source from above.
Card Shadow: box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1), 0px 10px 20px rgba(0, 0, 0, 0.1);
Button Hover Shadow: A very subtle box-shadow: 0px 5px 15px rgba(0, 124, 240, 0.2); using the accent color.
Part 3: Page Anatomy & Component Specification
This is the top-to-bottom build plan for the single page.

Section 1: The Hero
Layout: A div with min-height: 90vh, using flexbox to center content.
Components & Copy:
Headline: Use the TextReveal component.
split='word'
from='bottom'
delay={0.05}
H1 Text: "Branding that Connects. Websites that Convert."
Sub-headline: Fades in 500ms after the headline.
Text: "We are a design and growth studio for passionate founders. We build the strategic brand and digital presence you need to unlock your true business potential."
CTA Button: Shadcn Button (default variant).
Text: "Explore Our Thinking"
Section 2: The Technology Marquee
Layout: A div with padding-top: 24px and padding-bottom: 24px.
Component: Use the VelocityScroll component.
default_velocity={-2}
Wrap this in a single parent component.
Content: A single child div containing monochrome SVG logos of your tech stack (Figma, React, OpenAI, etc.).
Styling: Default logo color is #666666. On div:hover of the parent component, all logos smoothly transition to #EAEAEA.
Section 3: The Conceptual Case Studies
Section Title: Use TextReveal. Text: "Our Explorations."
Layout: A 2x2 CSS Grid with a 48px gap.
Custom Card Component (Critical Task):
Structure: Shadcn Card base. Inside, two divs are position: absolute.
Interaction: Implement the Hover-Scroll logic: onMouseEnter, crossfade from the static thumbnail div to the div containing an auto-playing <video> of the website scroll. onMouseLeave, fade back.
The "Maker's Mark": On one card thumbnail, overlay the CircularText component in the bottom-right corner.
Size: 80px width/height.
spinDuration={30}
onHover='pause'
Text: "• CELERAI CONCEPT •"
Modal: Clicking a card's "View Concept" button triggers a Shadcn Dialog containing more project details.
Section 4: The Approach
Section Title: Use TextReveal. Text: "Our Approach."
Layout: A 3-column CSS Grid with a 32px gap.
Content: Each column contains:
Icon: A simple, line-art SVG icon (32x32px, color #007CF0).
H3 Title: e.g., "1. Strategic Branding"
Body Text: A short, clear description.
Section 5: The Finale
Layout: A 2-column CSS Grid with a 64px gap, vertically centered.
Left Column:
Component: The Earth (Globe).
Configuration:
baseColor={[0.113, 0.486, 0.941]} (matches #1D7CF0)
glowColor={[0.113, 0.486, 0.941]}
dark={0.9}
scale={1.2}
Right Column:
Headline: Use TextReveal. Text: "Ready to Build Your Future?"
Body Text: "My name is [Your Name]. Celerai Studio was founded to be the partner I wish I had: one that merges world-class design with a relentless focus on business growth. If you're a founder who shares that passion, let's talk."
Contact Button: A large Shadcn Button (size="lg") that is a mailto: link.
Text: "Start the Conversation"